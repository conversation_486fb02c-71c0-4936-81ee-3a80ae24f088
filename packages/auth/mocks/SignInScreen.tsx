/* eslint-disable react-native/no-inline-styles */
import React, {useRef, useState} from 'react';
import {
  Alert,
  ImageBackground,
  Keyboard,
  Platform,
  StatusBar,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
  KeyboardAvoidingView,
} from 'react-native';
import {Button, Text, TextInput, useTheme} from 'react-native-paper';

type LoginFormValues = {
  username: string;
  password: string;
};

// Mock CopyrightText component that uses theme
const CopyrightTextMock = () => {
  const theme = useTheme();
  return (
    <Text style={{fontSize: 11, color: theme.colors.onSurfaceVariant}}>
      © 2024 App
    </Text>
  );
};

// Mock ReloadAppButton component that uses theme
const ReloadAppButtonMock = () => {
  const theme = useTheme();
  return (
    <Button
      mode="contained-tonal"
      onPress={() => Alert.alert('Reload App', 'This is a mock reload button')}
      style={{
        backgroundColor: theme.colors.surfaceVariant,
        borderRadius: 24,
      }}
      labelStyle={{color: theme.colors.onSurfaceVariant}}
      icon="reload"
    />
  );
};

export const SignInScreenMock = () => {
  const theme = useTheme();
  const refUsername = useRef<any>();
  const refPassword = useRef<any>();
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [loadingLogin, setLoadingLogin] = useState(false);
  const [secureTextEntry, setSecureTextEntry] = useState(true);

  const onSubmit = async ({username, password}: LoginFormValues) => {
    if (username.length === 0) {
      Alert.alert('Vui lòng nhập tên đăng nhập');
      refUsername.current?.focus();
      return;
    } else if (password.length === 0) {
      Alert.alert('Vui lòng nhập mật khẩu');
      setTimeout(() => {
        refPassword.current?.focus();
      }, 500);
      return;
    }

    setLoadingLogin(true);

    // Mock login process
    setTimeout(() => {
      setLoadingLogin(false);
      Alert.alert('Mock Login', `Logged in as: ${username}`);
    }, 2000);
  };

  // Create dynamic styles using theme
  const dynamicStyles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    formContainer: {
      flexDirection: 'column',
      backgroundColor: theme.colors.surface,
      paddingHorizontal: 20,
      paddingVertical: 24,
      borderRadius: 20,
      width: '100%',
      maxWidth: 400,
      shadowOffset: {width: 0, height: 12},
      shadowOpacity: 0.12,
      shadowRadius: 24,
      elevation: 4,
      shadowColor: theme.colors.shadow,
    },
    label: {
      color: theme.colors.onSurface,
      fontWeight: '500',
      marginBottom: 2,
    },
    input: {
      borderRadius: 8,
      backgroundColor: theme.colors.surface,
      borderWidth: 1,
      borderColor: theme.colors.outline,
    },
    loginButton: {
      height: 48,
      marginTop: 20,
      justifyContent: 'center',
      backgroundColor: theme.colors.primary,
    },
    citizenButton: {
      marginTop: 4,
      borderColor: theme.colors.primary,
    },
    endpointText: {
      marginTop: 4,
      color: theme.colors.onSurfaceVariant,
      fontSize: 12,
    },
  });

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={dynamicStyles.container}>
        {/* Mock gradient background */}
        <View
          style={[
            StyleSheet.absoluteFill,
            {backgroundColor: theme.colors.primaryContainer},
          ]}
        />

        {/* Mock background images with theme-aware overlays */}
        <View
          style={[
            styles.bgLoginTop,
            {backgroundColor: theme.colors.primary, opacity: 0.1},
          ]}
        />
        <View
          style={[
            styles.bgCity,
            {backgroundColor: theme.colors.tertiary, opacity: 0.1},
          ]}
        />

        <View style={styles.reloadButtonContainer}>
          <ReloadAppButtonMock />
        </View>

        <KeyboardAvoidingView
          style={styles.centered}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <View style={dynamicStyles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={dynamicStyles.label}>Tài khoản</Text>
              <TextInput
                placeholder="Nhập tài khoản"
                theme={{
                  roundness: 8,
                  colors: {
                    primary: theme.colors.primary,
                    onSurfaceVariant: theme.colors.onSurfaceVariant,
                  },
                }}
                style={dynamicStyles.input}
                underlineColor="transparent"
                ref={refUsername}
                value={username}
                returnKeyLabel="Tiếp"
                returnKeyType="next"
                onSubmitEditing={() => refPassword.current?.focus()}
                onChangeText={(text: string) =>
                  setUsername(text.trim().toLowerCase())
                }
                right={
                  <TextInput.Icon
                    icon="account"
                    iconColor={theme.colors.onSurfaceVariant}
                  />
                }
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            <View style={[styles.inputGroup, {marginTop: 16}]}>
              <Text style={dynamicStyles.label}>Mật khẩu</Text>
              <TextInput
                placeholder="Nhập mật khẩu"
                onChangeText={(text: string) => setPassword(text)}
                secureTextEntry={secureTextEntry}
                theme={{
                  roundness: 8,
                  colors: {
                    primary: theme.colors.primary,
                    onSurfaceVariant: theme.colors.onSurfaceVariant,
                  },
                }}
                style={dynamicStyles.input}
                value={password}
                underlineColor="transparent"
                returnKeyLabel="Tiếp"
                returnKeyType="go"
                onSubmitEditing={() => {
                  onSubmit({username, password});
                }}
                ref={refPassword}
                right={
                  <TextInput.Icon
                    icon={secureTextEntry ? 'eye' : 'eye-off'}
                    iconColor={theme.colors.onSurfaceVariant}
                    onPress={() => {
                      setSecureTextEntry(!secureTextEntry);
                    }}
                  />
                }
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            <Button
              theme={{roundness: 2}}
              style={dynamicStyles.loginButton}
              mode="contained"
              contentStyle={{marginTop: 4}}
              loading={loadingLogin}
              onPress={() => {
                onSubmit({username, password});
              }}>
              Đăng nhập
            </Button>
            <Button
              style={dynamicStyles.citizenButton}
              mode="outlined"
              theme={{
                roundness: 2,
                colors: {
                  outline: theme.colors.primary,
                },
              }}
              contentStyle={{marginTop: 4}}
              loading={loadingLogin}
              onPress={() => {
                Alert.alert(
                  'Chuyển đổi chế độ',
                  'Đang chuyển sang chế độ đăng nhập công dân...',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        Alert.alert(
                          'Mock Navigation',
                          'Navigate to CitizenLogin',
                        );
                      },
                    },
                  ],
                  {cancelable: false},
                );
              }}>
              Đăng nhập chức năng công dân
            </Button>
          </View>
          <View style={styles.footer}>
            <CopyrightTextMock />
            <Text style={dynamicStyles.endpointText}>[MOCK_ENDPOINT]</Text>
          </View>
        </KeyboardAvoidingView>
      </View>
    </TouchableWithoutFeedback>
  );
};

// Static styles that don't depend on theme
const styles = StyleSheet.create({
  bgLoginTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: 450,
    zIndex: 1,
  },
  bgCity: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: 238,
    zIndex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
    zIndex: 2,
  },
  inputGroup: {
    gap: 4,
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  reloadButtonContainer: {
    position: 'absolute',
    top: 40,
    right: 24,
    zIndex: 20,
  },
});
