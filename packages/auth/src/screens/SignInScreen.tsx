/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef} from 'react';
import {
  Alert,
  ImageBackground,
  Keyboard,
  Platform,
  StatusBar,
  TouchableWithoutFeedback,
  View,
  StyleSheet,
  KeyboardAvoidingView,
} from 'react-native';
import {useNavigation, NavigationProp} from '@react-navigation/native';
import {AuthStackParamList} from '../navigation/AuthNavigator';
import {Button, Text, TextInput, useTheme} from 'react-native-paper';
import {useAuthStore, useConfigStore} from '@ac-mobile/common';
import {Assets} from '../assets';
import {API_CONFIG, ENDPOINT} from '../api/api-config';
import {CopyrightText} from '../components/CopyrightText';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {ReloadAppButton} from '../components/ReloadAppButton';

type LoginFormValues = {
  username: string;
  password: string;
};

const STORAGE_KEY = 'LOGIN_CREDENTIALS';

const SignInScreen = () => {
  const navigation = useNavigation<NavigationProp<AuthStackParamList>>();
  const {setAuth, setLoading} = useAuthStore();
  const {setConfig} = useConfigStore();
  const theme = useTheme();
  const refUsername = useRef<any>();
  const refPassword = useRef<any>();
  const [username, setUsername] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [loadingLogin, setLoadingLogin] = React.useState(false);
  const [secureTextEntry, setSecureTextEntry] = React.useState(true);

  // Create dynamic styles using theme
  const dynamicStyles = React.useMemo(
    () =>
      StyleSheet.create({
        formContainer: {
          flexDirection: 'column',
          backgroundColor: theme.colors.surface,
          paddingHorizontal: 20,
          paddingVertical: 24,
          borderRadius: 20,
          width: '100%',
          maxWidth: 400,
          shadowOffset: {width: 0, height: 12},
          shadowOpacity: 0.12,
          shadowRadius: 24,
          elevation: 4,
          shadowColor: theme.colors.shadow,
        },
        label: {
          color: theme.colors.onSurface,
          fontWeight: '500',
          marginBottom: 2,
        },
        input: {
          borderRadius: 8,
          backgroundColor: theme.colors.surface,
          borderWidth: 1,
          borderColor: theme.colors.outline,
        },
        endpointText: {
          marginTop: 4,
          color: theme.colors.onSurfaceVariant,
          fontSize: 12,
        },
      }),
    [theme],
  );

  // Load credentials from device
  useEffect(() => {
    const loadCredentials = async () => {
      try {
        const creds = await AsyncStorage.getItem(STORAGE_KEY);
        if (creds) {
          const {username, password} = JSON.parse(creds);
          setUsername(username);
          setPassword(password);
        }
      } catch (e) {
        // Ignore
      }
    };
    loadCredentials();

    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor('transparent');
      StatusBar.setTranslucent(true);
    }
    StatusBar.setBarStyle('light-content');
  }, []);

  // Save credentials to device
  const saveCredentials = async (username: string, password: string) => {
    try {
      await AsyncStorage.setItem(
        STORAGE_KEY,
        JSON.stringify({username, password}),
      );
    } catch (e) {
      // Ignore
    }
  };

  const onSubmit = async ({username, password}: LoginFormValues) => {
    if (username.length === 0) {
      Alert.alert('Vui lòng nhập tên đăng nhập');
      refUsername.current.focus();
      return;
    } else if (password.length === 0) {
      Alert.alert('Vui lòng nhập mật khẩu');
      setTimeout(() => {
        refPassword.current.focus();
      }, 500);
      return;
    }

    setLoadingLogin(true);

    try {
      setLoading('loading');

      const getToken = await fetch(`${API_CONFIG.ENDPOINT}/User/GetToken`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userName: username,
          password: password,
        }),
      });

      if (getToken.ok) {
        const getTokenData = await getToken.json();
        const {accessToken, refreshToken, ...metadata} = getTokenData.data;
        const getUser = await fetch(
          `${API_CONFIG.ENDPOINT}/User/GetListUserMaster?UserID=${metadata.userID}`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          },
        );

        if (getUser.ok) {
          const getUserData = await getUser.json();
          if (getTokenData.statusCode === '00') {
            const userData = getUserData.data.find(
              (user: any) => user.laDonViChinh === true,
            );

            setAuth({
              ...getTokenData.data,
              user: {
                ...(userData || getUserData.data[0]),
                metadata,
              } as any,
            } as any);
            await saveCredentials(username, password);
          } else {
            Alert.alert(
              'Có lỗi khi lấy thông tin người dùng, vui lòng thử lại!',
            );
          }
        } else {
          Alert.alert('Có lỗi đăng nhập xảy ra, vui lòng đăng nhập lại!');
        }
      } else {
        Alert.alert(
          'Đăng nhập không thành công, vui lòng xem lại tên tài khoản/ mật khẩu!',
        );
      }
      setLoading('success');
    } catch (error) {
      setLoading('error');
      console.log('Auth error', error);
      Alert.alert('Thông tin đăng nhập không chính xác, vui lòng thử lại !!');
    } finally {
      setLoadingLogin(false);
    }
  };

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
      <View style={styles.container}>
        <ImageBackground
          source={Assets.bgGradient}
          style={StyleSheet.absoluteFill}
          resizeMode="cover"
        />
        <ImageBackground
          source={Assets.bgLoginTop}
          style={styles.bgLoginTop}
          resizeMode="cover"
        />
        <ImageBackground
          source={Assets.bgCity}
          style={styles.bgCity}
          resizeMode="contain"
        />
        <View style={styles.reloadButtonContainer}>
          <ReloadAppButton />
        </View>
        <KeyboardAvoidingView
          style={styles.centered}
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
          <View style={dynamicStyles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={dynamicStyles.label}>Tài khoản</Text>
              <TextInput
                placeholder="Nhập tài khoản"
                theme={{
                  roundness: 8,
                  colors: {
                    primary: theme.colors.primary,
                    onSurfaceVariant: theme.colors.onSurfaceVariant,
                  },
                }}
                style={dynamicStyles.input}
                underlineColor="transparent"
                ref={refUsername}
                value={username}
                returnKeyLabel="Tiếp"
                returnKeyType="next"
                onSubmitEditing={() => refPassword.current.focus()}
                onChangeText={(text: string) =>
                  setUsername(text.trim().toLocaleLowerCase())
                }
                right={
                  <TextInput.Icon
                    icon="account"
                    color={theme.colors.onSurfaceVariant}
                  />
                }
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            <View style={[styles.inputGroup, {marginTop: 16}]}>
              <Text style={dynamicStyles.label}>Mật khẩu</Text>
              <TextInput
                placeholder="Nhập mật khẩu"
                onChangeText={(text: string) => setPassword(text)}
                secureTextEntry={secureTextEntry}
                theme={{
                  roundness: 8,
                  colors: {
                    primary: theme.colors.primary,
                    onSurfaceVariant: theme.colors.onSurfaceVariant,
                  },
                }}
                style={dynamicStyles.input}
                value={password}
                underlineColor="transparent"
                returnKeyLabel="Tiếp"
                returnKeyType="go"
                onSubmitEditing={() => {
                  onSubmit({username, password});
                }}
                ref={refPassword}
                right={
                  <TextInput.Icon
                    icon={secureTextEntry ? 'eye' : 'eye-off'}
                    color={theme.colors.onSurfaceVariant}
                    onPress={() => {
                      setSecureTextEntry(!secureTextEntry);
                    }}
                  />
                }
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            <Button
              theme={{roundness: 2}}
              style={styles.loginButton}
              mode="contained"
              contentStyle={{marginTop: 4}}
              loading={loadingLogin}
              onPress={() => {
                onSubmit({username, password});
              }}>
              Đăng nhập
            </Button>
            <Button
              style={{marginTop: 4}}
              mode="outlined"
              theme={{roundness: 2}}
              contentStyle={{marginTop: 4}}
              loading={loadingLogin}
              onPress={() => {
                // Set login mode to citizen then navigate appropriately
                setConfig('loginMode', 'citizen');

                // Alert user about the switch
                Alert.alert(
                  'Chuyển đổi chế độ',
                  'Đang chuyển sang chế độ đăng nhập công dân...',
                  [
                    {
                      text: 'OK',
                      onPress: () => {
                        // Navigate to CitizenLogin screen
                        navigation.navigate('CitizenLogin' as never);
                      },
                    },
                  ],
                  {cancelable: false},
                );
              }}>
              Đăng nhập chức năng công dân
            </Button>
          </View>
          <View style={styles.footer}>
            <CopyrightText />
            <Text style={dynamicStyles.endpointText}>[{ENDPOINT}]</Text>
          </View>
        </KeyboardAvoidingView>
      </View>
    </TouchableWithoutFeedback>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  bgLoginTop: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: 450,
    zIndex: 1,
  },
  bgCity: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    width: '100%',
    height: 238,
    zIndex: 1,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 8,
    zIndex: 2,
  },
  inputGroup: {
    gap: 4,
  },
  loginButton: {
    height: 48,
    marginTop: 20,
    justifyContent: 'center',
  },
  footer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },

  reloadButtonContainer: {
    position: 'absolute',
    top: 40,
    right: 24,
    zIndex: 20,
  },
  reloadButton: {
    borderRadius: 24,
    elevation: 2,
  },
});

export default SignInScreen;
