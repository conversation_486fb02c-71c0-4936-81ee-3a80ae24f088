// ColorPickerIconMap.ts
// This file defines the icon color map for CommonTemplates/ColorPickerIcon

export const COLOR_PICKER_ICON_MAP = [
  {
    keywords: ['hộ khẩu', 'cư trú', 'nhân khẩu'],
    paint: ['#8DB6FC', '#366AE2'],
    icon: 'home-city',
  },
  {
    keywords: ['giấy tờ', 'tài liệu', 'văn bản'],
    paint: ['#A7FFEB', '#1DE9B6'],
    icon: 'file-document-outline',
  },
  {
    keywords: ['căn cước', 'cmnd', 'cccd', 'chứng minh'],
    paint: ['#FFF59D', '#FBC02D'],
    icon: 'card-account-details-outline',
  },
  {
    keywords: ['hộ chiếu', 'passport'],
    paint: ['#FFAB91', '#D32F2F'],
    icon: 'passport',
  },
  {
    keywords: ['khai sinh', 'gi<PERSON><PERSON> khai sinh', 'gi<PERSON><PERSON> chứng sinh'],
    paint: ['#FFD6E0', '#FF69B4'],
    icon: 'baby-face-outline',
  },
  {
    keywords: ['kết hôn', 'giấy kết hôn', 'đăng ký kết hôn', 'marriage'],
    paint: ['#E1BEE7', '#8E24AA'],
    icon: 'ring',
  },
  {
    keywords: ['quảng cáo', 'quang cao', 'advertising'],
    paint: ['#FFF3E0', '#FF9800'],
    icon: 'bullhorn-outline',
  },
  {
    keywords: ['trò chơi điện tử', 'game', 'trò chơi', 'điện tử'],
    paint: ['#B3E5FC', '#0288D1'],
    icon: 'gamepad-variant-outline',
  },
  {
    keywords: ['thủ tục', 'thu tuc', 'procedure', 'dvc'],
    paint: ['#BDBDBD', '#757575'],
    icon: 'clipboard-list-outline',
  },
  // Add more as needed
];

export function getIconAndPaintForName(name: string): {
  paint: string[];
  icon: string;
} {
  if (!name)
    return {paint: ['#BDBDBD', '#757575'], icon: 'clipboard-list-outline'};
  const lower = name.toLowerCase();
  for (const entry of COLOR_PICKER_ICON_MAP) {
    if (entry.keywords.some(keyword => lower.includes(keyword))) {
      return {paint: entry.paint, icon: entry.icon};
    }
  }
  // Fallback: use a procedure/certificate icon and neutral color
  return {paint: ['#BDBDBD', '#757575'], icon: 'clipboard-list-outline'};
}
